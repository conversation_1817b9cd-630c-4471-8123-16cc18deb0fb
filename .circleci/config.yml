# Javascript Node CircleCI 2.0 configuration file
#
# Check https://circleci.com/docs/2.0/language-javascript/ for more details
#
version: 2.1

parameters:
  remote_spec_pattern:
    type: string
    default: "./remote/specs/**/*.spec.js"
  localdb_spec_pattern:
    type: string
    default: "./local/specs/**/*.spec.js"
  enable_remote_e2e:
    type: boolean
    default: true
  enable_localdb_e2e:
    type: boolean
    default: true
  manual_trigger_branch_env:
    type: string
    default: "master"

defaults: &defaults
  working_directory: /tmp/repo
  docker:
    # specify the version you desire here
    - image: cimg/node:22.18.0-browsers
  resource_class: large

run_e2e_and_save_artifacts: &run_e2e_and_save_artifacts
  steps:
    - browser-tools/install_chrome:
        version:99.0.4844.51
        timeout: 5m
#    - browser-tools/install_chromedriver:
#        timeout: 5m
    - run:
        command: |
          google-chrome --version
          chromedriver --version
    - when:
        condition: << pipeline.parameters.enable_remote_e2e >>
        steps:
          - attach_workspace:
              at: /tmp/repo
          - run:
              name: Install linux dependencies
              command: |
                sudo apt-get install build-essential libcairo2-dev libpango1.0-dev libjpeg-dev libgif-dev librsvg2-dev || true
          - run:
              name: Check remote cbioportal connection
              command: |
                cd cbioportal-test
                sh ./utils/check-connection.sh --url=$CBIOPORTAL_URL
          - run:
              name: Run end-to-end tests
              environment:
                FRONTEND_TEST_USE_LOCAL_DIST: true
                WEBPACK_PARALLEL: false
              command: |
                cd /tmp/repo/cbioportal-frontend
                yarn serveDist &
                cd /tmp/repo/cbioportal-test
                sh ./utils/check-connection.sh --url=https://localhost:3000 --insecure='true'
                cd /tmp/repo/cbioportal-frontend/end-to-end-test
                ../scripts/env_vars.sh
                eval "$(../scripts/env_vars.sh)"
                yarn run test-webdriver-manager-remote
          - run:
              name: Make sure all screenshots are tracked (otherwise the test will always be successful)
              command: |
                cd cbioportal-frontend
                for f in end-to-end-test/remote/screenshots/reference/*.png; do git ls-files --error-unmatch $f > /dev/null 2> /dev/null || (echo -e "\033[0;31m $f not tracked \033[0m" && touch screenshots_not_tracked); done; ls screenshots_not_tracked > /dev/null 2> /dev/null && exit 1 || exit 0
          -  store_artifacts:
               path: /tmp/repo/cbioportal-frontend/end-to-end-test/remote/screenshots
               destination: /screenshots
          -  store_artifacts:
               path: /tmp/repo/cbioportal-frontend/end-to-end-test/shared/image-compare
               destination: /image-compare
          -  store_artifacts:
               path: /tmp/repo/cbioportal-frontend/end-to-end-test/remote/error
               destination: /errorShots
          - store_test_results:
              path: /tmp/repo/cbioportal-frontend/end-to-end-test/remote/junit
          - store_artifacts:
              path: /tmp/repo/cbioportal-frontend/end-to-end-test/remote/junit
          - store_artifacts:
              path: /tmp/repo/cbioportal-frontend/end-to-end-test/shared/imageCompare.html
              destination: /imageCompare.html
          - store_artifacts:
              path: /tmp/repo/cbioportal-frontend/end-to-end-test/remote/junit/completeResults.json
              destination: /completeResults.json
          - store_artifacts:
              path: /tmp/repo/cbioportal-frontend/end-to-end-test/remote/junit/errors
              destination: /errors
    - unless:
        condition: << pipeline.parameters.enable_remote_e2e >>
        steps:
          - run: echo "Job Ignored via enable_remote_e2e"
          - run: exit 1

orbs:
  browser-tools: circleci/browser-tools@2.3.1
jobs:
  api_sync:
    <<: *defaults
    environment:
      MANUAL_TRIGGER_BRANCH_ENV: << pipeline.parameters.manual_trigger_branch_env >>
    steps:
      - attach_workspace:
          at: /tmp/repo
      - run:
          name: Check that all api responses are still the same
          command: |
            cd cbioportal-frontend
            bash src/test/check_api_sync.sh

  unit_tests_main:
    <<: *defaults
    steps:
      - attach_workspace:
          at: /tmp/repo
      - run:
          name: Run junit tests main
          environment:
            JEST_JUNIT_OUTPUT_DIR: ./junit/unit/main/
            JEST_JUNIT_OUTPUT_NAME: test-results-unit-main.xml
            NODE_OPTIONS: --max-old-space-size=4096
            DISABLE_SOURCEMAP: true
            WEBPACK_PARALLEL: false
          command: |
            cd cbioportal-frontend
            yarn run testMain
      - store_test_results:
          path: ./junit/unit/main/

  unit_tests_packages:
    <<: *defaults
    steps:
      - attach_workspace:
          at: /tmp/repo
      - run:
          name: Run jest junit tests
          environment:
            DISABLE_SOURCEMAP: true
            NODE_OPTIONS: --max-old-space-size=4096
            WEBPACK_PARALLEL: false
          command: |
            cd cbioportal-frontend
            yarn run testPackagesCI
      - store_test_results:
          path: /tmp/junit/module/

  check_forgotten_spec_only_statements:
    <<: *defaults
    steps:
      - attach_workspace:
          at: /tmp/repo
      # Ignore doTest.only in oql parser
      - run:
          name: "Check forgotten `.only` statements"
          command: |
            cd cbioportal-frontend
            ! grep '\\.only' $(find ./src ./end-to-end-test -type f -name '*spec*' | grep -v node_modules) | grep -v 'doTest.only = function' | grep -v expectedParsedResult

  check_incorrect_import_statements:
    <<: *defaults
    steps:
      - attach_workspace:
          at: /tmp/repo
      - run:
          name: "Check incorrect package import statements"
          command: |
            cd cbioportal-frontend
            yarn run checkIncorrectImportStatements

  e2e_tests:
    <<: *defaults
    environment:
      MANUAL_TRIGGER_BRANCH_ENV: << pipeline.parameters.manual_trigger_branch_env >>
      SPEC_FILE_PATTERN: << pipeline.parameters.remote_spec_pattern >>
      JUNIT_REPORT_PATH: ./remote/junit/
      SCREENSHOT_DIRECTORY: ./remote/screenshots
      CBIOPORTAL_URL: https://www.cbioportal.org
      CHROMEDRIVER_CUSTOM_PATH: /usr/local/bin/chromedriver
    <<: *run_e2e_and_save_artifacts

  e2e_tests_against_master:
    <<: *defaults
    environment:
      SPEC_FILE_PATTERN: ./remote/specs/**/*.spec.js
      JUNIT_REPORT_PATH: ./remote/junit/
      SCREENSHOT_DIRECTORY: ./remote/screenshots
      CBIOPORTAL_URL: https://master.cbioportal.org
      CHROMEDRIVER_CUSTOM_PATH: /usr/local/bin/chromedriver
    <<: *run_e2e_and_save_artifacts

  prettier:
    <<: *defaults
    environment:
      MANUAL_TRIGGER_BRANCH_ENV: << pipeline.parameters.manual_trigger_branch_env >>
    steps:
      - attach_workspace:
          at: /tmp/repo
      - run:
          name: 'Check code style with Prettier'
          command: |
            cd cbioportal-frontend
            yarn add -W prettier@1.19.1
            yarn run prettierCheckCircleCI

  pull_cbioportal_test_codebase:
    machine:
      image: ubuntu-2204:2024.08.1
    resource_class: medium
    working_directory: /tmp/repo
    steps:
      - run:
          name: Checkout cbioportal/cbioportal-test
          environment:
            TEST_REPO_URL: https://github.com/cbioportal/cbioportal-test
          command: |
            git clone ${TEST_REPO_URL}
      - persist_to_workspace:
          root: /tmp/repo
          paths: cbioportal-test

  checkout:
    machine:
      image: ubuntu-2204:2024.08.1
    resource_class: medium
    working_directory: /tmp/repo
    steps:
      - checkout:
          path: /tmp/repo/cbioportal-frontend
      - persist_to_workspace:
          root: /tmp/repo
          paths: cbioportal-frontend

  build_frontend:
    <<: *defaults
    working_directory: /tmp/repo
    environment:
      BRANCH_ENV: master
    steps:
      - attach_workspace:
          at: /tmp/repo
      - run:
          name: Generate cache key for dist
          command: |
            cd cbioportal-frontend
            echo "Concatenate all source files to use as hash key."
            cat yarn.lock $(find src/ -type f | sort) $(find packages/ -type f | sort) webpack.config.js vendor-bundles.webpack.config.js > has_source_changed
      - restore_cache:
          keys:
            - v11-dependencies-plus-dist-{{ checksum "cbioportal-frontend/has_source_changed" }}
            - v11-dependencies-{{ checksum "cbioportal-frontend/yarn.lock" }}
      - run:
          name: Download dependencies
          command: |
            cd cbioportal-frontend
            yarn
            yarn buildModules
      - save_cache:
          name: Save dependency cache
          key: v11-dependencies-{{ checksum "cbioportal-frontend/yarn.lock" }}
          paths:
            - cbioportal-frontend/node_modules
      - run:
          name: Build frontend application
          environment:
            DISABLE_SOURCEMAP: true
            NO_PARALLEL: true
          command: |
            cd cbioportal-frontend
            ls dist || yarn run build
      - run:
          name: Build frontend tests
          command: |
            cd cbioportal-frontend/end-to-end-test
            ls node_modules || yarn install --frozen-lockfile --ignore-engines
      - save_cache:
          name: Save dependency plus dist cache
          key: v11-dependencies-plus-dist-{{ checksum "cbioportal-frontend/has_source_changed" }}
          paths:
            - cbioportal-frontend/node_modules
            - cbioportal-frontend/dist
            - cbioportal-frontend/common-dist
      - persist_to_workspace:
          root: /tmp/repo
          paths:
            - cbioportal-frontend

  e2e_localdb_tests:
    machine:
      image: ubuntu-2204:2024.08.1
    resource_class: large
    working_directory: /tmp/repo
    environment:
      BACKEND_PORT: 8080
      FRONTEND_PORT: 3000
      DOCKER_IMAGE_MYSQL: cbioportal/mysql:8.0-database-test
      DOCKER_IMAGE_CBIOPORTAL: cbioportal/cbioportal:master
    steps:
      - attach_workspace:
          at: /tmp/repo
      - run:
          name: Generate keycloak config
          environment:
            STUDIES: 'ascn_test_study study_hg38 teststudy_genepanels study_es_0 lgg_ucsf_2014_test_generic_assay'
          command: |
            git clone https://github.com/cbioportal/cbioportal-docker-compose.git
            cd cbioportal-test
            ./utils/gen-keycloak-config.sh --studies="$STUDIES" --template='/tmp/repo/cbioportal-docker-compose/dev/keycloak/keycloak-config.json' --out='/tmp/repo/keycloak-config-generated.json'
      - run:
          name: Start backend server with keycloak
          environment:
            KEYCLOAK_CONFIG_PATH: '/tmp/repo/keycloak-config-generated.json'
            APPLICATION_PROPERTIES_PATH: '/tmp/repo/cbioportal-frontend/end-to-end-test/local/runtime-config/portal.properties'
          command: |
            cd cbioportal-test
            nohup ./scripts/docker-compose.sh --portal_type='keycloak' >> /tmp/repo/docker-compose-logs.txt 2>&1 &
      - run:
          name: Check keycloak connection at localhost
          command: |
            cd cbioportal-test
            ./utils/check-connection.sh --url=localhost:8081 --max_retries=50
      - run:
          name: Check backend connection at localhost
          command: |
            cd cbioportal-test
            ./utils/check-connection.sh --url=localhost:${BACKEND_PORT} --max_retries=50
      - run:
          name: Run e2e localdb tests
          environment:
            SPEC_FILE_PATTERN: << pipeline.parameters.localdb_spec_pattern >>
            CBIOPORTAL_URL: http://localhost:8080
            JUNIT_REPORT_PATH: ./local/junit/
            SCREENSHOT_DIRECTORY: ./local/screenshots
            PORTAL_SOURCE_DIR: /tmp/repo/cbioportal-frontend
            TEST_HOME: /tmp/repo/cbioportal-frontend/end-to-end-test/local
            CHROMEDRIVER_CUSTOM_PATH: /usr/local/bin/chromedriver
          command: |
            cd $PORTAL_SOURCE_DIR
            $TEST_HOME/docker_compose/test.sh
      - run:
          environment:
            TEST_HOME: /tmp/repo/cbioportal-frontend/end-to-end-test/local
          name: Make sure all screenshots are tracked (otherwise the test will always be successful)
          command: |
            cd cbioportal-frontend
            for f in end-to-end-test/local/screenshots/reference/*.png; do
              git ls-files --error-unmatch $f > /dev/null 2> /dev/null ||
              (echo -e "\033[0;31m $f not tracked \033[0m" && touch screenshots_not_tracked);
            done;
            ls screenshots_not_tracked > /dev/null 2> /dev/null && exit 1 || exit 0
      - store_artifacts:
          path: /tmp/repo/docker-compose-logs.txt
      - store_artifacts:
          path: /tmp/repo/cbioportal-frontend/end-to-end-test/local/screenshots
          destination: /screenshots
      - store_artifacts:
          path: /tmp/repo/cbioportal-frontend/end-to-end-test/shared/image-compare
          destination: /image-compare
      - store_artifacts:
          path: /tmp/repo/cbioportal-frontend/end-to-end-test/local/errorShots
          destination: /errorShots
      - store_test_results:
          path: /tmp/repo/cbioportal-frontend/end-to-end-test/local/junit
      - store_artifacts:
          path: /tmp/repo/cbioportal-frontend/end-to-end-test/local/junit
      - store_artifacts:
          path: /tmp/repo/cbioportal-frontend/end-to-end-test/shared/imageCompare.html
          destination: /imageCompare.html
      - store_artifacts:
          path: /tmp/repo/cbioportal-frontend/end-to-end-test/local/junit/completeResults.json
          destination: /completeResults.json
      - store_artifacts:
          path: /tmp/repo/cbioportal-frontend/end-to-end-test/local/junit/errors
          destination: /errors
            

workflows:
  tests:
    jobs:
      - pull_cbioportal_test_codebase
      - checkout
      - prettier:
          requires:
            - checkout
          filters:
            branches:
              ignore:
                - master
                - release-*
      - check_forgotten_spec_only_statements:
          requires:
            - checkout
      - check_incorrect_import_statements:
          requires:
            - checkout
      - build_frontend:
          requires:
            - checkout
#      - e2e_localdb_tests:
#          requires:
#            - pull_cbioportal_test_codebase
#            - build_frontend
      - e2e_tests:
          requires:
            - pull_cbioportal_test_codebase
            - build_frontend
#      - api_sync:
#          requires:
#            - build_frontend
#      - unit_tests_packages:
#          requires:
#            - build_frontend
#      - unit_tests_main:
#          requires:
#            - build_frontend


  nightly:
    triggers:
      - schedule:
          cron: "0 22 * * *"
          filters:
            branches:
              only:
                - master
    jobs:
      - pull_cbioportal_test_codebase
      - checkout
      - build_frontend:
          requires:
            - checkout
      - e2e_localdb_tests:
          requires:
            - pull_cbioportal_test_codebase
            - build_frontend
      - e2e_tests:
          requires:
            - pull_cbioportal_test_codebase
            - build_frontend
      - e2e_tests_against_master:
          requires:
            - pull_cbioportal_test_codebase
            - build_frontend
      - api_sync:
          requires:
            - build_frontend
      - unit_tests_main:
          requires:
            - build_frontend
      - unit_tests_packages:
          requires:
            - build_frontend
