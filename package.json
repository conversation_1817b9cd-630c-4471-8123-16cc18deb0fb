{"name": "cbioportal-frontend", "private": true, "version": "3.3.299", "workspaces": {"packages": [".", "packages/*"]}, "description": "", "pre-commit": ["prettier<PERSON>ix<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "scripts": {"start": "lerna run watch --parallel", "startSSL": "lerna run watchSSL --parallel", "mergeTests": "./scripts/api-test_env.sh && node api-e2e/merge-tests.js", "apitests": "cd ./api-e2e && npx tsc && yarn run mergeTests && node ./run.js", "watch": "./scripts/env_vars.sh && eval \"$(./scripts/env_vars.sh)\" && cross-env NODE_ENV=development webpack-dev-server --compress", "watchSSL": "./scripts/env_vars.sh && eval \"$(./scripts/env_vars.sh)\" && cross-env NODE_ENV=development NODE_OPTIONS=--max-old-space-size=4096 webpack-dev-server --compress --https", "clean": "rimraf dist tsDist common-dist", "updatePackageVersion": "./scripts/update_package_version.sh", "build": "./scripts/env_vars.sh && eval \"$(./scripts/env_vars.sh)\" && yarn run buildAll", "buildAll": "yarn run buildModules && yarn run buildMain", "buildMain": "yarn run clean && yarn run compileOqlParser && yarn run buildDLL:prod && cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=4096 webpack", "buildModules": "lerna run build --ignore=cbioportal-frontend --stream", "publishModules": "lerna publish from-git --yes", "buildDLL:prod": "yarn run clean && cross-env NODE_ENV=production webpack --config vendor-bundles.webpack.config.js", "buildDLL:dev": "yarn run clean && cross-env NODE_ENV=development webpack --config vendor-bundles.webpack.config.js", "build:size": "yarn run clean && yarn run buildDLL:prod && cross-env NODE_ENV=production webpack --json > stats.json", "buildBootstrap": "sass --style :compressed src/globalStyles/bootstrap-entry.scss src/globalStyles/prefixed-bootstrap.min.css", "heroku-postbuild": "yarn run build && yarn add pushstate-server@3.0.1 -g", "updateAPI": "yarn run fetchAPI && yarn run buildAPI && yarn run updateOncoKbAPI && yarn run updateGenomeNexusAPI", "convertToSwagger2": "./scripts/convert_to_swagger2.sh && yarn run extendSwagger2Converter", "fetchAPILocal": "export CBIOPORTAL_URL=http://localhost:8082 && curl -s -L -k ${CBIOPORTAL_URL}/api/v3/api-docs/public > packages/cbioportal-ts-api-client/src/generated/CBioPortalAPI-docs.json && curl -s -L -k ${CBIOPORTAL_URL}/api/v3/api-docs/internal | json > packages/cbioportal-ts-api-client/src/generated/CBioPortalAPIInternal-docs.json && yarn run convertToSwagger2", "fetchAPI": "./scripts/env_vars.sh && eval \"$(./scripts/env_vars.sh)\" && curl -s -L -k ${CBIOPORTAL_URL}/api/v3/api-docs/public  > packages/cbioportal-ts-api-client/src/generated/CBioPortalAPI-docs.json && curl -s -L -k ${CBIOPORTAL_URL}/api/v3/api-docs/internal | json  > packages/cbioportal-ts-api-client/src/generated/CBioPortalAPIInternal-docs.json && yarn run convertToSwagger2", "extendSwagger2Converter": "node scripts/extend_converter.js packages/cbioportal-ts-api-client/src/generated CBioPortalAPI CBioPortalAPIInternal", "buildAPI": "node scripts/generate-api.js packages/cbioportal-ts-api-client/src/generated CBioPortalAPI CBioPortalAPIInternal", "updateOncoKbAPI": "yarn run fetchOncoKbAPI && yarn run buildOncoKbAPI", "fetchOncoKbAPI": "curl -s -k https://www.oncokb.org/api/v1/v2/api-docs?group=Public%20APIs | json | grep -v basePath | grep -v termsOfService | grep -v host > packages/oncokb-ts-api-client/src/generated/OncoKbAPI-docs.json", "buildOncoKbAPI": "node scripts/generate-api.js packages/oncokb-ts-api-client/src/generated OncoKbAPI", "updateG2SAPI": "yarn run fetchG2SAPI && yarn run buildG2SAPI", "fetchG2SAPI": "curl -s -k http://g2s.genomenexus.org/v2/api-docs?group=api > packages/genome-nexus-ts-api-client/src/generated/Genome2StructureAPI-docs.json", "buildG2SAPI": "node scripts/generate-api.js packages/genome-nexus-ts-api-client/src/generated Genome2StructureAPI", "updateGenomeNexusAPI": "yarn run fetchGenomeNexusAPI && yarn run buildGenomeNexusAPI", "fetchGenomeNexusAPI": "./scripts/env_vars.sh && eval \"$(./scripts/env_vars.sh)\" && curl -s -L -k ${GENOME_NEXUS_URL}/v2/api-docs | json | grep -v basePath | grep -v termsOfService | grep -v host > packages/genome-nexus-ts-api-client/src/generated/GenomeNexusAPI-docs.json && curl -s -L -k ${GENOME_NEXUS_URL}/v2/api-docs?group=internal | json | grep -v basePath | grep -v termsOfService | grep -v host > packages/genome-nexus-ts-api-client/src/generated/GenomeNexusAPIInternal-docs.json", "buildGenomeNexusAPI": "node scripts/generate-api.js packages/genome-nexus-ts-api-client/src/generated GenomeNexusAPI GenomeNexusAPIInternal", "updateHotspotGenes": "./scripts/get_hotspot_genes.sh > src/shared/static-data/hotspotGenes.json", "compileOqlParser": "cd src/shared/lib/oql && pegjs oql-parser.pegjs", "serveDist": "./scripts/serve_dist.sh", "serveDistLocalDb": "./end-to-end-test/local/runtime-config/serve_dist.sh", "testTime": "start_time=`date +%s` && karma start karma.conf.js && echo run time is $(expr `date +%s` - $start_time) s", "test": "yarn run testMain", "check-env": "echo ", "testMain": "jest $GREP --env=jsdom --runInBand --ci --reporters=default --reporters=jest-junit", "testPackagesCI": "JEST_JUNIT_UNIQUE_OUTPUT_NAME=true JEST_JUNIT_OUTPUT_DIR=/tmp/junit/module/ JEST_JUNIT_OUTPUT_NAME=unit-test-module yarn run testPackages", "testPackages": "lerna run test --ignore=cbioportal-frontend --stream", "test:watch": "yarn run testMain:watch", "testMain:watch": "yarn run testMain --watch", "test:debug": "yarn run test --debug --browsers=Chrome_with_debugging --single-run=false", "waitForNetlifyPreview": "node scripts/wait_for_netlify_preview.js", "findNetlifyDeployId": "node scripts/find_netlify_deploy_id.js", "prettierFixCurrentChanges": "STAGED_AND_CHANGED_FILES=$(git diff HEAD --name-only --cached --diff-filter=d) && ([ -z \"$STAGED_AND_CHANGED_FILES\" ] && echo \"Nothing to prettify\" || (yarn run prettier --write $(echo $STAGED_AND_CHANGED_FILES) && git add -f $(echo $STAGED_AND_CHANGED_FILES)))", "prettierCheckCircleCI": "./scripts/env_vars.sh && eval \"$(./scripts/env_vars.sh)\" && git remote add cbio-repo https://github.com/cBioPortal/cbioportal-frontend.git && git fetch cbio-repo $(echo $BRANCH_ENV) && CHANGED_FILES=$(git diff cbio-repo/$(echo $BRANCH_ENV) --name-only --diff-filter=d) && [ ! -z \"$CHANGED_FILES\" ] && yarn run prettier -c $(echo $CHANGED_FILES) && git remote remove cbio-repo", "prettierFixLocal": "CHANGED_FILES=$(git diff $(echo $BRANCH_ENV) --name-only --diff-filter=d) && ([ ! -z \"$CHANGED_FILES\" ] && yarn run prettier --write $(echo $CHANGED_FILES) || echo \"Nothing to prettify\")", "prettierAll": "yarn run prettier --write $(git ls-files | grep '\\(.js\\|.ts\\|.scss\\|.css\\)')", "checkIncorrectImportStatements": "./scripts/check_incorrect_import_statements.sh", "syncmock": "node src/test/fetchMockData.js --diff", "e2e:spinup": "./scripts/e2e.sh", "e2e:local": "export RETRIES=0 && export CBIOPORTAL_URL=http://localhost:8080 && export && export SCREENSHOT_DIRECTORY=/local/screenshots/ && cd end-to-end-test && yarn run test-webdriver-manager-local", "e2e:remote": "export RETRIES=0 export CBIOPORTAL_URL=https://www.cbioportal.org && export RETRIES=0 && export SCREENSHOT_DIRECTORY=/remote/screenshots/ &&  rm -r -f end-to-end-test/shared/results && cd end-to-end-test && yarn run test-webdriver-manager-remote", "e2e:report": "npx http-server end-to-end-test -o /shared/imageCompare.html -p 8089", "localChromeDriver": "chromedriver"}, "engines": {"node": "18.20.7", "yarn": "1.22.5"}, "resolutions": {"mobx": "^6.0.0", "mobx-react": "6.0.0"}, "author": "", "license": "AGPL-3.0-or-later", "dependencies": {"3dmol": "^1.3.7", "@babel/core": "7.4.4", "@babel/plugin-syntax-dynamic-import": "^7.8.3", "@babel/plugin-transform-modules-commonjs": "^7.4.4", "@babel/preset-env": "^7.4.4", "@babel/preset-react": "^7.0.0", "@datadog/browser-logs": "^5.28.0", "@rollup/plugin-commonjs": "^22.0.0", "@rollup/plugin-image": "^2.1.1", "@rollup/plugin-json": "^4.1.0", "@rollup/plugin-node-resolve": "^13.3.0", "@testing-library/react": "12.1", "@types/chai": "3.5.2", "@types/chai-enzyme": "^0.6.2", "@types/chart.js": "^2.4.6", "@types/classnames": "0.0.32", "@types/color-convert": "^2.0.0", "@types/d3-scale": "^2.0.0", "@types/d3-scale-chromatic": "^1.3.0", "@types/detect-browser": "^1.6.0", "@types/enzyme": "^3.10.8", "@types/enzyme-adapter-react-16": "^1.0.6", "@types/expired-storage": "^1.0.0", "@types/google.analytics": "0.0.39", "@types/history": "^4.7.8", "@types/jest": "^27.0.2", "@types/jquery": "^3.3.31", "@types/js-combinatorics": "0.5.29", "@types/jspdf": "^1.1.31", "@types/jstree": "^3.3.35", "@types/lodash": "4.14.104", "@types/lolex": "1.5.31", "@types/numeral": "0.0.22", "@types/pako": "1.0.1", "@types/pdfobject": "2.0.5", "@types/qs": "^6.9.5", "@types/raphael": "2.1.30", "@types/rc-tooltip": "^3.7.3", "@types/react": "^16.14.15", "@types/react-bootstrap": "^0.32.15", "@types/react-collapse": "^4.0.2", "@types/react-color": "^3.0.4", "@types/react-dom": "^16.8.2", "@types/react-fontawesome": "1.5.0", "@types/react-grid-layout": "^0.16.5", "@types/react-helmet": "^6.1.0", "@types/react-overlays": "^0.6.9", "@types/react-router": "3.0.2", "@types/react-router-dom": "^5.1.6", "@types/react-select": "^5.0.1", "@types/react-sortable-hoc": "^0.6.5", "@types/react-spinkit": "^1.1.35", "@types/react-table": "^6.8.7", "@types/react-toastify": "^4.1.0", "@types/react-transition-group": "^2.0.14", "@types/react-virtualized": "^9.18.7", "@types/recompose": "^0.20.4", "@types/reselect": "^2.0.27", "@types/route-parser": "0.0.0", "@types/seamless-immutable": "^6.1.2", "@types/seedrandom": "2.4.27", "@types/sinon": "^4.1.3", "@types/superagent": "^2.0.36", "addthis-snippet": "^1.0.1", "autobind-decorator": "^2.1.0", "autoprefixer": "^6.7.0", "axios": "^1.7.8", "babel-loader": "8.0.5", "babel-plugin-transform-es2015-modules-umd": "^6.22.0", "babel-polyfill": "^6.22.0", "babel-register": "^6.22.0", "better-react-spinkit": "^2.0.0-6", "bind-decorator": "^1.0.11", "bootstrap": "3.4.1", "bootstrap-loader": "^1.2.0-beta.1", "bootstrap-sass": "3.4.1", "bowser": "^1.7.1", "bundle-loader": "^0.5.4", "cbioportal-clinical-timeline": "^0.3.94", "cbioportal-frontend-commons": "^0.5.76", "cbioportal-ts-api-client": "^0.9.78", "cbioportal-utils": "^0.3.48", "chart.js": "^2.6.0", "classnames": "^2.2.5", "clinical-timeline": "0.0.30", "color-convert": "^2.0.1", "contrast": "^1.0.1", "copy-webpack-plugin": "^9.0.1", "cross-env": "^3.1.4", "css-loader": "^2.1.1", "cssnano": "^3.10.0", "csvtojson": "^2.0.10", "d3": "3.5.6", "d3-dsv": "1.0.8", "d3-scale": "^2.0.0", "d3-scale-chromatic": "^1.3.3", "datatables.net": "^1.10.13", "dotenv": "^4.0.0", "enzyme": "^3.11.0", "enzyme-adapter-react-16": "^1.12.1", "enzyme-custom-wrappers": "^1.0.0", "expired-storage": "^1.0.2", "express": "^4.16.4", "fast_array_intersect": "^1.1.0", "file-loader": "^3.0.1", "fixed-data-table": "^0.6.3", "fmin": "^0.0.2", "font-awesome": "^4.7.0", "fork-ts-checker-webpack-plugin": "^6.3.3", "genome-nexus-ts-api-client": "^1.1.35", "git-revision-webpack-plugin": "^5.0.0", "history": "4.10.1", "html-webpack-plugin": "^5.3.2", "igv": "^2.11.2", "imports-loader": "^0.8.0", "jStat": "^1.7.0", "javascript-natural-sort": "^0.7.1", "jquery": "3.6.0", "jquery-migrate": "3.0.0", "js-combinatorics": "^0.5.2", "js-event-bus": "^1.1.1", "json-fn": "^1.1.1", "jsonpath": "^1.1.1", "jspdf": "^1.3.3", "jstree": "^3.3.4", "jszip": "^3.7.1", "lerna": "3.19.0", "less": "^2.7.2", "linear-algebra": "^3.1.4", "little-loader": "^0.2.0", "loader-utils": "^1.0.2", "localforage": "^1.9.0", "lodash": "^4.17.11", "lolex": "^1.6.0", "measure-text": "0.0.4", "memoize-weak-decorator": "^1.0.3", "mini-css-extract-plugin": "^0.12.0", "mixpanel-browser": "^2.18.0", "mobx": "^6.0.0", "mobx-logger": "^0.7.1", "mobx-react": "6.0.0", "mobx-react-devtools": "6.1.1", "mobx-react-lite": "3.0.1", "mobx-react-router": "4.1.0", "mobx-utils": "6.0.1", "numeral": "^2.0.6", "object-sizeof": "^1.2.0", "oncokb-frontend-commons": "^0.0.32", "oncokb-styles": "~1.4.2", "oncokb-ts-api-client": "^1.3.8", "oncoprintjs": "^6.0.7", "pako": "2.0.2", "parameter-validator": "^1.0.2", "path-browserify": "^1.0.1", "pathway-mapper": "^2.3.0", "pdfobject": "^2.0.201604172", "pegjs": "^0.10.0", "plotly.js": "^1.42.5", "pluralize": "^7.0.0", "postcss-loader": "^1.2.2", "postcss-url": "^8.0.0", "precss": "^1.4.0", "progress-bar-webpack-plugin": "^2.1.0", "pushstate-server": "3.0.1", "query-string": "^6.13.6", "range-ts": "^0.1.5", "raw-loader": "^0.5.1", "rc-animate": "^3.1.1", "rc-tooltip": "^5.0.2", "rc-trigger": "^5.2.1", "rc-util": "^5.8.0", "react": "^16.4", "react-addons-pure-render-mixin": "^15.4.2", "react-addons-test-utils": "^15.4.2", "react-bootstrap": "^0.31.5", "react-bootstrap-slider": "^2.0.1", "react-clipboard.js": "^1.0.1", "react-collapse": "^4.0.3", "react-color": "^2.18.1", "react-column-resizer": "^1.1.9", "react-dom": "^16.6", "react-draggable": "^3.3.0", "react-file-download": "^0.3.2", "react-fontawesome": "^1.5.0", "react-grid-layout": "^0.16.6", "react-height": "^3.0.0", "react-helmet": "^6.1.0", "react-if": "^2.1.0", "react-json-to-table": "^0.1.5", "react-markdown": "^7.0.1", "react-mfb": "^0.6.0", "react-motion": "^0.4.7", "react-mutation-mapper": "^0.8.120", "react-overlays": "0.7.4", "react-portal": "^4.2.0", "react-rangeslider": "^2.1.0", "react-renderif": "^1.0.2", "react-resize-detector": "^0.5.0", "react-reveal": "^1.2.2", "react-router": "5.2.0", "react-router-dom": "^5.2.0", "react-select": "^3.0.4", "react-select1": "npm:react-select@1.3.0", "react-sortable-hoc": "^1.9.1", "react-spinkit": "^3.0.0", "react-text-truncate": "^0.9.0", "react-toastify": "^9.1.2", "react-tooltip": "^3.2.2", "react-transition-group": "^2.5.1", "react-virtualized": "^9.20.1", "react-zeroclipboard": "^3.2.2", "reactable": "^0.14.1", "reactour": "^1.15.0", "recompose": "^0.22.0", "regression": "^2.0.1", "rehype-raw": "^6.1.0", "rehype-sanitize": "^5.0.0", "remark-gfm": "^3.0.1", "render-if": "^0.1.1", "reselect": "^2.5.4", "resolve-url-loader": "^1.6.1", "responsive-fixed-data-table": "^2.0.0", "rollup": "^2.73.0", "rollup-plugin-node-externals": "^4.0.0", "rollup-plugin-postcss": "^2.9.0", "rollup-plugin-sourcemaps": "^0.6.3", "rollup-plugin-typescript2": "^0.31.2", "rollup-plugin-web-worker-loader": "^1.6.1", "route-parser": "0.0.5", "sass": "^1.32.4", "sass-loader": "10.1.1", "sass-resources-loader": "^1.3.1", "save-svg-as-png": "^1.4.17", "script-loader": "^0.7.0", "seamless-immutable": "^7.0.1", "seedrandom": "^2.4.3", "shallowequal": "^1.1.0", "snap": "^1.1.0", "source-map-loader": "^0.1.6", "style-loader": "^0.23.1", "styled-components": "^4.2.1", "superagent": "^3.8.3", "superagent-cache": "^3.0.1", "svg2pdf.js": "github:cbioportal/svg2pdf.js#v1.3.3-cbio-patch-1", "svgsaver": "0.9.0", "swagger-client": "^3.8.22", "ts-loader": "4.0.0", "tslib": "^1.11.1", "typed-css-modules": "^0.6.4", "typed-css-modules-webpack-plugin": "^0.2.0", "typescript": "4.0.3", "underscore": "^1.8.3", "url": "^0.11.0", "url-loader": "^1.1.2", "venn.js": "^0.2.20", "victory": "30.0.0", "webpack": "^5.55.1", "webpack-cli": "^4.8.0", "webpack-raphael": "github:Dmitry<PERSON>aranov<PERSON>y/raphael#v2.3.0", "word-wrap": "^1.2.3", "xml2js": "^0.5.0"}, "devDependencies": {"@types/cheerio": "^0.22.23", "@types/react-test-renderer": "^17.0.1", "argparse": "^1.0.9", "babel-plugin-rewire": "^1.0.0", "chai": "^4.3.4", "chai-enzyme": "^1.0.0-beta.1", "cheerio": "^1.0.0-rc.5", "colors": "^1.1.2", "concurrently": "^5.2.0", "deep-equal-in-any-order": "^1.0.10", "es-abstract": "1.18.0-next.2", "expect": "^1.20.2", "expect-jsx": "5.0.0", "http-server": "0.11.1", "identity-obj-proxy": "^3.0.0", "jasmine-core": "^3.9.0", "jest": "^27.2.1", "jest-canvas-mock": "^2.3.1", "jest-environment-jsdom": "^27.2.0", "jest-junit": "^13.2.0", "json": "^9.0.6", "netlify": "^8.0.1", "node-fetch": "^2.6.1", "pre-commit": "^1.2.2", "prettier": "1.19.1", "react-element-to-jsx-string": "14.3.1", "react-test-renderer": "^17.0.2", "request": "^2.88.0", "sinon": "^4.1.3", "swagger-js-codegen": "git+https://github.com/cBioPortal/swagger-js-codegen.git#0362f4a1e2d116ad6dffc36e4b57cdfbc93956cf", "ts-jest": "^27.0.5", "ts-node": "^10.2.1", "webpack-dev-server": "^4.1.0", "yargs": "^6.6.0"}, "packageManager": "yarn@1.22.5"}