{"name": "cbioportal-clinical-timeline", "description": "cBioPortal Clinical Timeline", "version": "0.3.94", "main": "dist/index.js", "module": "dist/index.es.js", "jsnext:main": "dist/index.es.js", "typings": "dist/index.d.ts", "styles": "dist/styles.css", "engines": {"node": "15.2.1", "yarn": "1.22.5"}, "files": ["dist"], "author": "cBioPortal", "license": "AGPL-3.0-or-later", "repository": "cBioPortal/cbioportal-frontend", "scripts": {"build": "yarn run tcm && cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=2048 yarn run rollup", "start": "yarn run watch", "watch": "concurrently \"yarn run tcm:watch\" \"yarn run rollup:watch\"", "watchSSL": "yarn run watch", "rollup": "rollup -c rollup.config.ts", "rollup:watch": "yarn run rollup --watch", "tcm": "tcm -p src/**/*.module.scss", "tcm:watch": "yarn run tcm --watch", "prepare": "yarn run build", "test": "cross-env jest $GREP --env=jsdom --runInBand --ci --reporters=default --reporters=jest-junit --passWithNoTests", "test:watch": "yarn run test --watch"}, "peerDependencies": {"mobx": "^6.0.0", "mobx-react": "^6.0.0", "mobx-react-lite": "^2.0.0", "react": "^15.0.0 || ^16.0.0", "react-dom": "^15.0.0 || ^16.0.0"}, "dependencies": {"autobind-decorator": "^2.1.0", "cbioportal-frontend-commons": "^0.5.76", "lodash": "^4.17.11", "react-bootstrap": "^0.31.5", "react-overlays": "0.7.4", "typescript": "4.0.3"}}